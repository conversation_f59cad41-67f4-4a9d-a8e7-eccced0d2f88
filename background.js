
chrome.runtime.onInstalled.addListener(() => {
  // Initialize default settings for local files and web pages
  chrome.storage.sync.set({
    localFileGridSettings: {
      color: '#000000',
      gridSizeX: 50,
      gridSizeY: 50,
      opacity: 0.5,
      horizontalOffset: 0,
      verticalOffset: 0,
      isVisible: false
    }
  });
});

// Add listener to handle local file navigation

// Simplified background script with robust error handling
chrome.runtime.onInstalled.addListener(() => {
  console.log('Artist Grid Overlay extension installed');
});

// Remove webNavigation usage to resolve potential errors
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  try {
    // Central message handling if needed
    sendResponse({ status: 'success' });
  } catch (error) {
    console.error('Background script error:', error);
    sendResponse({ status: 'error', message: error.toString() });
  }
  return true; // Allow asynchronous response
});

