
// Robust message sending utility
const MessageSender = {
  sendToActiveTab(message) {
    return new Promise((resolve, reject) => {
      // Verify chrome.tabs availability
      if (!chrome.tabs) {
        reject(new Error('Chrome tabs API unavailable'));
        return;
      }

      // Query active tab
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        // Check for runtime errors
        if (chrome.runtime.lastError) {
          console.error('Tab query error:', chrome.runtime.lastError);
          reject(chrome.runtime.lastError);
          return;
        }

        // Validate tabs array
        if (!tabs || tabs.length === 0) {
          reject(new Error('No active tab found'));
          return;
        }

        const activeTab = tabs[0];

        // Send message to content script
        try {
          chrome.tabs.sendMessage(activeTab.id, message, (response) => {
            if (chrome.runtime.lastError) {
              console.error('Message sending error:', chrome.runtime.lastError);
              reject(chrome.runtime.lastError);
              return;
            }

            resolve(response);
          });
        } catch (error) {
          console.error('Message sending exception:', error);
          reject(error);
        }
      });
    });
  }
};

// Popup UI Controller
class PopupController {
  constructor() {
    this.initializeElements();
    this.attachEventListeners();
    this.loadSettings();
  }

  // Initialize DOM element references
  initializeElements() {
    this.elements = {
      gridColor: document.getElementById('grid-color'),
      gridSizeX: document.getElementById('grid-size-x'),
      gridSizeY: document.getElementById('grid-size-y'),
      gridOpacity: document.getElementById('grid-opacity'),
      horizontalOffset: document.getElementById('horizontal-offset'),
      verticalOffset: document.getElementById('vertical-offset'),
      toggleGridButton: document.getElementById('toggle-grid'),
      errorDisplay: document.getElementById('error-message')
    };

    this.valueDisplays = {
      gridSizeX: document.getElementById('grid-size-x-value'),
      gridSizeY: document.getElementById('grid-size-y-value'),
      gridOpacity: document.getElementById('grid-opacity-value'),
      horizontalOffset: document.getElementById('horizontal-offset-value'),
      verticalOffset: document.getElementById('vertical-offset-value')
    };
  }

  // Attach event listeners to controls
  attachEventListeners() {
    const inputHandlers = [
      { element: 'gridColor', key: 'color' },
      { element: 'gridSizeX', key: 'gridSizeX' },
      { element: 'gridSizeY', key: 'gridSizeY' },
      { element: 'gridOpacity', key: 'opacity' },
      { element: 'horizontalOffset', key: 'horizontalOffset' },
      { element: 'verticalOffset', key: 'verticalOffset' }
    ];

    inputHandlers.forEach(({ element, key }) => {
      this.elements[element].addEventListener('input', (e) => {
        const value = this.parseInputValue(e.target);
        this.updateValueDisplay(element, value);
        this.updateSetting(key, value);
      });
    });

    this.elements.toggleGridButton.addEventListener('click', () => {
      this.toggleGrid();
    });
  }

  // Parse input value based on element type
  parseInputValue(target) {
    switch(target.type) {
      case 'range':
        return Number(target.value);
      case 'color':
        return target.value;
      default:
        return target.value;
    }
  }

  // Update value display
  updateValueDisplay(elementName, value) {
    const display = this.valueDisplays[elementName];
    if (!display) return;

    switch(elementName) {
      case 'gridOpacity':
        display.textContent = `${Math.round(value * 100)}%`;
        break;
      default:
        display.textContent = value;
    }
  }

  // Load saved settings
  loadSettings() {
    chrome.storage.sync.get(['gridSettings'], (result) => {
      const settings = result.gridSettings || {};
      
      Object.entries(this.elements).forEach(([key, element]) => {
        if (element.type === 'range' || element.type === 'color') {
          element.value = settings[key] || element.defaultValue;
          this.updateValueDisplay(key, element.value);
        }
      });
    });
  }

  // Update a specific setting
  updateSetting(key, value) {
    this.sendMessage({
      action: 'updateSetting',
      key: key,
      value: value
    });
  }

  // Toggle grid visibility
  toggleGrid() {
    this.sendMessage({ action: 'toggleGrid' });
  }

  // Send message to content script
  sendMessage(message) {
    MessageSender.sendToActiveTab(message)
      .then(response => {
        console.log('Message sent successfully', response);
      })
      .catch(error => {
        this.displayError(`Failed to send message: ${error.message}`);
      });
  }

  // Display error message
  displayError(message) {
    if (this.elements.errorDisplay) {
      this.elements.errorDisplay.textContent = message;
      this.elements.errorDisplay.style.display = 'block';
      
      // Auto-hide error after 3 seconds
      setTimeout(() => {
        this.elements.errorDisplay.textContent = '';
        this.elements.errorDisplay.style.display = 'none';
      }, 3000);
    }
  }
}

// Initialize popup when DOM is fully loaded
document.addEventListener('DOMContentLoaded', () => {
  new PopupController();
});
