
// Artist Grid Overlay Content Script
// This script runs on web pages to create and manage the grid overlay

class GridOverlay {
  constructor() {
    this.gridElement = null;
    this.isVisible = false;
    this.settings = {
      color: '#000000',
      gridSizeX: 50,
      gridSizeY: 50,
      opacity: 0.5,
      horizontalOffset: 0,
      verticalOffset: 0
    };

    this.init();
  }

  // Initialize the grid overlay
  init() {
    this.loadSettings();
    this.createGridElement();
    this.setupMessageListener();
    this.setupImageDropHandling();

    // Handle page navigation and dynamic content
    this.observePageChanges();
  }

  // Load settings from storage
  loadSettings() {
    chrome.storage.sync.get(['gridSettings'], (result) => {
      if (result.gridSettings) {
        this.settings = { ...this.settings, ...result.gridSettings };
        this.updateGridStyles();
      }
    });
  }

  // Save settings to storage
  saveSettings() {
    chrome.storage.sync.set({ gridSettings: this.settings });
  }

  // Create the grid overlay element
  createGridElement() {
    // Remove existing grid if it exists
    this.removeGridElement();

    // Create new grid element
    this.gridElement = document.createElement('div');
    this.gridElement.id = 'artist-grid-overlay';
    this.gridElement.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      pointer-events: none;
      z-index: 999999;
      display: none;
    `;

    // Add to document
    document.documentElement.appendChild(this.gridElement);
    this.updateGridStyles();
  }

  // Remove grid element
  removeGridElement() {
    const existingGrid = document.getElementById('artist-grid-overlay');
    if (existingGrid) {
      existingGrid.remove();
    }
  }

  // Update grid visual styles based on current settings
  updateGridStyles() {
    if (!this.gridElement) return;

    const { color, gridSizeX, gridSizeY, opacity, horizontalOffset, verticalOffset } = this.settings;

    // Create grid pattern using CSS linear gradients
    const backgroundImage = `
      linear-gradient(to right, ${color} 1px, transparent 1px),
      linear-gradient(to bottom, ${color} 1px, transparent 1px)
    `;

    this.gridElement.style.backgroundImage = backgroundImage;
    this.gridElement.style.backgroundSize = `${gridSizeX}px ${gridSizeY}px`;
    this.gridElement.style.backgroundPosition = `${horizontalOffset}px ${verticalOffset}px`;
    this.gridElement.style.opacity = opacity;
  }

  // Toggle grid visibility
  toggleGrid() {
    this.isVisible = !this.isVisible;

    if (this.gridElement) {
      this.gridElement.style.display = this.isVisible ? 'block' : 'none';
    }

    // Save visibility state
    this.settings.isVisible = this.isVisible;
    this.saveSettings();

    return this.isVisible;
  }

  // Update a specific setting
  updateSetting(key, value) {
    this.settings[key] = value;
    this.updateGridStyles();
    this.saveSettings();
  }

  // Setup message listener for communication with popup
  setupMessageListener() {
    chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
      try {
        switch (message.action) {
          case 'toggleGrid':
            const newState = this.toggleGrid();
            sendResponse({ success: true, isVisible: newState });
            break;

          case 'updateSetting':
            this.updateSetting(message.key, message.value);
            sendResponse({ success: true });
            break;

          case 'getGridState':
            sendResponse({
              success: true,
              isVisible: this.isVisible,
              settings: this.settings
            });
            break;

          default:
            sendResponse({ success: false, error: 'Unknown action' });
        }
      } catch (error) {
        console.error('Grid overlay error:', error);
        sendResponse({ success: false, error: error.message });
      }

      return true; // Keep message channel open for async response
    });
  }

  // Observe page changes to maintain grid overlay
  observePageChanges() {
    // Handle dynamic content changes
    const observer = new MutationObserver((_mutations) => {
      // Check if our grid element was removed
      if (!document.getElementById('artist-grid-overlay')) {
        this.createGridElement();
        if (this.isVisible) {
          this.gridElement.style.display = 'block';
        }
      }
    });

    observer.observe(document.documentElement, {
      childList: true,
      subtree: true
    });

    // Handle page resize
    window.addEventListener('resize', () => {
      this.updateGridStyles();
    });
  }

  // Enhanced image detection for better overlay positioning
  detectImages() {
    const images = document.querySelectorAll('img');
    return Array.from(images).filter(img => {
      return img.complete && img.naturalWidth > 0 && img.naturalHeight > 0;
    });
  }

  // Handle drag and drop events for images
  setupImageDropHandling() {
    document.addEventListener('dragover', (e) => {
      e.preventDefault();
    });

    document.addEventListener('drop', (e) => {
      e.preventDefault();

      // Show grid when image is dropped
      if (e.dataTransfer.files.length > 0) {
        const file = e.dataTransfer.files[0];
        if (file.type.startsWith('image/')) {
          // Auto-show grid when image is dropped
          if (!this.isVisible) {
            this.toggleGrid();
          }
        }
      }
    });
  }
}

// Initialize grid overlay when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new GridOverlay();
  });
} else {
  new GridOverlay();
}
