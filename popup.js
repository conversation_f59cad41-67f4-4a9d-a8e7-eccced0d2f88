
// Robust message sending and error handling
function sendMessageToActiveTab(message) {
  return new Promise((resolve, reject) => {
    // Verify we have access to chrome.tabs
    if (!chrome.tabs) {
      reject(new Error('Chrome tabs API not available'));
      return;
    }

    // Query for the active tab
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      // Check for runtime errors
      if (chrome.runtime.lastError) {
        console.error('Tab query error:', chrome.runtime.lastError);
        reject(chrome.runtime.lastError);
        return;
      }

      // Validate tabs array
      if (!tabs || tabs.length === 0) {
        reject(new Error('No active tab found'));
        return;
      }

      const activeTab = tabs[0];

      // Send message to the content script
      try {
        chrome.tabs.sendMessage(activeTab.id, message, (response) => {
          // Check for any message sending errors
          if (chrome.runtime.lastError) {
            console.error('Message sending error:', chrome.runtime.lastError);
            reject(chrome.runtime.lastError);
            return;
          }

          // Resolve with the response
          resolve(response);
        });
      } catch (error) {
        console.error('Exception in sending message:', error);
        reject(error);
      }
    });
  });
}

// Initialize popup controls and event listeners
document.addEventListener('DOMContentLoaded', () => {
  // Get all input and control elements
  const controls = {
    gridColor: document.getElementById('grid-color'),
    gridSizeX: document.getElementById('grid-size-x'),
    gridSizeY: document.getElementById('grid-size-y'),
    gridOpacity: document.getElementById('grid-opacity'),
    horizontalOffset: document.getElementById('horizontal-offset'),
    verticalOffset: document.getElementById('vertical-offset'),
    toggleGridButton: document.getElementById('toggle-grid'),
    errorDisplay: document.getElementById('error-message')
  };

  // Value display elements
  const valueDisplays = {
    gridSizeX: document.getElementById('grid-size-x-value'),
    gridSizeY: document.getElementById('grid-size-y-value'),
    gridOpacity: document.getElementById('grid-opacity-value'),
    horizontalOffset: document.getElementById('horizontal-offset-value'),
    verticalOffset: document.getElementById('vertical-offset-value')
  };

  // Error handling utility
  function displayError(message) {
    if (controls.errorDisplay) {
      controls.errorDisplay.textContent = message;
      controls.errorDisplay.style.display = 'block';
      setTimeout(() => {
        controls.errorDisplay.textContent = '';
        controls.errorDisplay.style.display = 'none';
      }, 3000);
    }
  }

  // Load saved settings from storage
  function loadSettings() {
    chrome.storage.sync.get(['gridSettings'], (result) => {
      const settings = result.gridSettings || {};
      
      // Update input values
      controls.gridColor.value = settings.color || '#000000';
      controls.gridSizeX.value = settings.gridSizeX || 50;
      controls.gridSizeY.value = settings.gridSizeY || 50;
      controls.gridOpacity.value = settings.opacity || 0.5;
      controls.horizontalOffset.value = settings.horizontalOffset || 0;
      controls.verticalOffset.value = settings.verticalOffset || 0;

      // Update value displays
      updateValueDisplays();
    });
  }

  // Update value display elements
  function updateValueDisplays() {
    valueDisplays.gridSizeX.textContent = controls.gridSizeX.value;
    valueDisplays.gridSizeY.textContent = controls.gridSizeY.value;
    valueDisplays.gridOpacity.textContent = `${Math.round(controls.gridOpacity.value * 100)}%`;
    valueDisplays.horizontalOffset.textContent = controls.horizontalOffset.value;
    valueDisplays.verticalOffset.textContent = controls.verticalOffset.value;
  }

  // Create a generic setting update method
  function updateSetting(key, value) {
    sendMessageToActiveTab({
      action: 'updateSetting',
      key: key,
      value: value
    })
    .then((response) => {
      console.log(`Setting ${key} updated successfully:`, response);
    })
    .catch((error) => {
      console.error(`Failed to update ${key}:`, error);
      // Provide more helpful error messages
      if (error.message.includes('Could not establish connection')) {
        displayError(`Grid overlay not ready. Please refresh the page and try again.`);
      } else {
        displayError(`Failed to update ${key}: ${error.message}`);
      }
    });
  }

  // Attach event listeners to input controls
  function attachEventListeners() {
    // Color input
    controls.gridColor.addEventListener('input', (e) => {
      updateSetting('color', e.target.value);
    });

    // Grid Size X
    controls.gridSizeX.addEventListener('input', (e) => {
      const value = Number(e.target.value);
      valueDisplays.gridSizeX.textContent = value;
      updateSetting('gridSizeX', value);
    });

    // Grid Size Y
    controls.gridSizeY.addEventListener('input', (e) => {
      const value = Number(e.target.value);
      valueDisplays.gridSizeY.textContent = value;
      updateSetting('gridSizeY', value);
    });

    // Opacity
    controls.gridOpacity.addEventListener('input', (e) => {
      const value = Number(e.target.value);
      valueDisplays.gridOpacity.textContent = `${Math.round(value * 100)}%`;
      updateSetting('opacity', value);
    });

    // Horizontal Offset
    controls.horizontalOffset.addEventListener('input', (e) => {
      const value = Number(e.target.value);
      valueDisplays.horizontalOffset.textContent = value;
      updateSetting('horizontalOffset', value);
    });

    // Vertical Offset
    controls.verticalOffset.addEventListener('input', (e) => {
      const value = Number(e.target.value);
      valueDisplays.verticalOffset.textContent = value;
      updateSetting('verticalOffset', value);
    });

    // Toggle Grid Button
    controls.toggleGridButton.addEventListener('click', () => {
      console.log('Toggle grid button clicked');
      sendMessageToActiveTab({ action: 'toggleGrid' })
        .then((response) => {
          console.log('Grid toggled successfully:', response);
          if (response && response.success) {
            // Update button text based on grid state
            controls.toggleGridButton.textContent = response.isVisible ? 'Hide Grid' : 'Show Grid';
          }
        })
        .catch((error) => {
          console.error('Toggle grid error:', error);
          // Provide more helpful error messages
          if (error.message.includes('Could not establish connection')) {
            displayError(`Grid overlay not ready. Please refresh the page and try again.`);
          } else {
            displayError(`Failed to toggle grid: ${error.message}`);
          }
        });
    });
  }

  // Initialize popup
  function initPopup() {
    loadSettings();
    updateValueDisplays();
    attachEventListeners();
  }

  // Start initialization
  initPopup();
});
