
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Artist Grid Overlay</title>
  <style>
    body {
      width: 300px;
      padding: 10px;
      font-family: Arial, sans-serif;
    }
    .control-group {
      margin-bottom: 15px;
    }
    .control-group label {
      display: block;
      margin-bottom: 5px;
    }
    .range-value {
      display: inline-block;
      margin-left: 10px;
    }
  </style>
</head>
<body>
  <h2>Artist Grid Overlay</h2>
  
  <div class="control-group">
    <label for="grid-color">Grid Color</label>
    <input type="color" id="grid-color" value="#000000">
  </div>

  <div class="control-group">
    <label for="grid-size-x">Grid Size X 
      <span class="range-value" id="grid-size-x-value">50</span>px
    </label>
    <input type="range" id="grid-size-x" min="10" max="200" value="50">
  </div>

  <div class="control-group">
    <label for="grid-size-y">Grid Size Y 
      <span class="range-value" id="grid-size-y-value">50</span>px
    </label>
    <input type="range" id="grid-size-y" min="10" max="200" value="50">
  </div>

  <div class="control-group">
    <label for="grid-opacity">Opacity 
      <span class="range-value" id="grid-opacity-value">50%</span>
    </label>
    <input type="range" id="grid-opacity" min="0.1" max="1" step="0.1" value="0.5">
  </div>

  <div class="control-group">
    <label for="horizontal-offset">Horizontal Offset 
      <span class="range-value" id="horizontal-offset-value">0</span>px
    </label>
    <input type="range" id="horizontal-offset" min="-100" max="100" value="0">
  </div>

  <div class="control-group">
    <label for="vertical-offset">Vertical Offset 
      <span class="range-value" id="vertical-offset-value">0</span>px
    </label>
    <input type="range" id="vertical-offset" min="-100" max="100" value="0">
  </div>

  <div class="control-group">
    <button id="toggle-grid">Toggle Grid</button>
  </div>

  <div id="error-message" style="display: none; color: red; margin-top: 10px; font-size: 12px;"></div>

  <script src="popup.js"></script>
</body>
</html>
